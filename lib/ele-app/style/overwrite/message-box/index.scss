@use '../../../../style/themes/default.scss' as *;
@use '../../../../style/util.scss' as *;
@use './css-var.scss' as *;

@include set-el-message-box-var($ele);

/* MessageBox */
.el-overlay-message-box .el-message-box {
  padding: 0;
  max-width: eleVar('message-box', 'width');
  border-radius: eleVar('message-box', 'radius');
  background: elVar('bg-color', 'overlay');
  border: none;

  .el-message-box__header {
    padding: eleVar('message-box', 'header-padding');
    border-bottom: eleVar('message-box', 'header-border');
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }

  .el-message-box__title {
    flex: 1;
    color: eleVar('message-box', 'header-color');
    font-size: eleVar('message-box', 'header-font-size');
    font-weight: eleVar('message-box', 'header-font-weight');
    line-height: eleVar('message-box', 'header-line-height');
  }

  .el-message-box__headerbtn {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: eleVar('message-box', 'icon-size');
    height: eleVar('message-box', 'icon-size');
    line-height: eleVar('message-box', 'icon-size');
    color: eleVar('message-box', 'icon-color');
    font-size: eleVar('message-box', 'icon-font-size');
    border-radius: eleVar('message-box', 'icon-radius');
    transition: (color $transition-base, background-color $transition-base);
    box-sizing: border-box;
    position: static;
    right: auto;
    top: auto;

    .el-message-box__close {
      color: inherit;
    }

    &:hover {
      color: eleVar('message-box', 'icon-hover-color');
      background: eleVar('message-box', 'icon-hover-bg');
    }
  }

  .el-message-box__content {
    padding: eleVar('message-box', 'body-padding');
    box-sizing: border-box;
  }

  .el-message-box__status {
    flex-shrink: 0;
    font-size: eleVar('message-box', 'status-size');
  }

  .el-message-box__btns {
    padding: eleVar('message-box', 'footer-padding');
    border-top: eleVar('message-box', 'footer-border');
    box-sizing: border-box;
  }

  .el-message-box__input {
    padding: 0;
    margin: eleVar('message-box', 'input-margin');
    position: relative;
  }

  .el-message-box__errormsg {
    line-height: 18px;
    position: absolute;
    top: 100%;
    left: 0;
  }
}

.el-message-box .el-message-box__status.el-message-box-icon--info {
  color: elVar('color-primary');
}

.el-message-box__message {
  word-break: break-all;
}

body .el-overlay.is-message-box > .el-overlay-message-box {
  overflow-x: hidden;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* useMessageBox */
.ele-message-box > .el-message-box__content > .el-message-box__container {
  gap: 0;

  & > .el-message-box__status {
    width: auto;
    height: auto;
  }
}

.ele-message-box-icon {
  width: 1em;
  height: 1em;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

/* 容器 */
.ele-message-box-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  pointer-events: none;
  overflow: hidden;

  & > .el-overlay.is-message-box {
    pointer-events: auto;
    position: absolute;

    & > .el-overlay-message-box {
      position: absolute;
    }
  }

  &.is-hide {
    display: none;
  }
}
