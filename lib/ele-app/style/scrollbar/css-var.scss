@use '../../../style/util.scss' as *;
// 美化滚动条样式针对谷歌选择器
$scrollbar-webkit-selector: 'html.is-webkit' !default;

/* 滚动条主题变量 */
@mixin set-scrollbar-var($var) {
  @include set-ele-var('scrollbar', $var);
}

/* 滚动条样式 */
@mixin scrollbar-style($selector: '') {
  #{if($selector == '', '*', $selector)} {
    scrollbar-width: thin;
    scrollbar-color: eleVar('scrollbar', 'color') eleVar('scrollbar', 'bg');
  }

  #{$selector}::-webkit-scrollbar {
    width: eleVar('scrollbar', 'size');
    height: eleVar('scrollbar', 'size');
  }

  #{$selector}::-webkit-scrollbar-thumb {
    border: eleVar('scrollbar', 'padding') solid transparent;
    border-radius: eleVar('scrollbar', 'radius');
    background-color: eleVar('scrollbar', 'color');
    background-clip: padding-box;

    &:hover {
      background-color: eleVar('scrollbar', 'hover-color');
    }
  }

  #{$selector}::-webkit-scrollbar-track {
    background: eleVar('scrollbar', 'bg');
  }

  #{$selector}::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* 美化滚动条样式 */
@mixin set-scrollbar-style($selector) {
  @if $selector == '' {
    @include scrollbar-style();

    #{$scrollbar-webkit-selector},
    #{$scrollbar-webkit-selector} * {
      scrollbar-width: auto;
      scrollbar-color: auto;
    }
  } @else if $selector == 'pc' {
    @media (hover: hover) and (pointer: fine) {
      @include scrollbar-style();

      #{$scrollbar-webkit-selector},
      #{$scrollbar-webkit-selector} * {
        scrollbar-width: auto;
        scrollbar-color: auto;
      }
    }
  } @else if $selector != 'never' {
    @include scrollbar-style($selector);

    #{$scrollbar-webkit-selector}#{$selector},
    #{$scrollbar-webkit-selector} #{$selector} {
      scrollbar-width: auto;
      scrollbar-color: auto;
    }
  }
}
