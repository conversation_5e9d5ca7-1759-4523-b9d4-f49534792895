"use strict";
const vue = require("vue");
const ReceiverView = require("../../ele-config-provider/components/receiver-view");
const _hoisted_1 = { class: "ele-admin-body" };
const _hoisted_2 = ["data-vk"];
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{ name: "LayoutSkeleton" },
  __name: "layout-skeleton",
  props: {
    /** logo是否位于顶栏 */
    isHeaderLogo: Boolean
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(["ele-admin-layout", { "is-row-direction": !__props.isHeaderLogo }])
      }, [
        __props.isHeaderLogo ? vue.renderSlot(_ctx.$slots, "head", { key: 0 }) : vue.renderSlot(_ctx.$slots, "side", { key: 1 }),
        vue.createVNode(vue.unref(ReceiverView), {
          class: vue.normalizeClass(["ele-admin-main", { "is-row-direction": __props.isHeaderLogo }]),
          wrapPosition: false
        }, {
          default: vue.withCtx((result) => [
            __props.isHeaderLogo ? vue.renderSlot(_ctx.$slots, "side", { key: 0 }) : vue.renderSlot(_ctx.$slots, "head", { key: 1 }),
            vue.createElementVNode("div", _hoisted_1, [
              result && result.subject ? vue.renderSlot(_ctx.$slots, "tabs", { key: 0 }) : vue.createCommentVNode("", true),
              vue.createElementVNode("div", {
                "data-vk": `05fd${result.subject ?? ""}`,
                class: "ele-admin-wrapper"
              }, [
                vue.renderSlot(_ctx.$slots, "body")
              ], 8, _hoisted_2)
            ])
          ]),
          _: 3
        }, 8, ["class"]),
        vue.renderSlot(_ctx.$slots, "default")
      ], 2);
    };
  }
});
module.exports = _sfc_main;
