<template>
  <div>
    <ele-card header="表格列宽拖拽调整演示">
      <div style="margin-bottom: 16px">
        <el-alert
          title="功能说明"
          description="鼠标悬停在表头列的右边缘，会出现拖拽手柄，拖拽可以调整列宽。支持普通表格和虚拟表格。"
          type="info"
          show-icon
          :closable="false"
        />
      </div>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="普通表格" name="normal">
          <ele-pro-table
            :height="400"
            :virtual="false"
            row-key="userId"
            :columns="columns"
            :datasource="datasource"
            :show-overflow-tooltip="true"
            :pagination="{ pageSize: 20 }"
            :border="true"
            :toolbar="{ theme: 'default' }"
            cache-key="column-resize-demo-normal"
            @header-dragend="handleHeaderDragend"
          />
        </el-tab-pane>

        <el-tab-pane label="虚拟表格" name="virtual">
          <ele-pro-table
            :height="400"
            :virtual="true"
            row-key="userId"
            :columns="columns"
            :datasource="datasource"
            :show-overflow-tooltip="true"
            :pagination="{ pageSize: 1000 }"
            :border="true"
            :toolbar="{ theme: 'default' }"
            cache-key="column-resize-demo-virtual"
            @header-dragend="handleHeaderDragend"
          />
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import type { Column } from 'ele-admin-plus/es/ele-data-table/types';
  import { EleMessage } from 'ele-admin-plus';

  defineOptions({ name: 'ColumnResizeDemo' });

  /** 当前选中的标签页 */
  const activeTab = ref('normal');

  /** 表格列配置 */
  const columns = reactive<Column[]>([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'userId',
      label: '用户ID',
      width: 80,
      sortable: 'custom'
    },
    {
      prop: 'username',
      label: '用户账号',
      width: 120,
      sortable: 'custom'
    },
    {
      prop: 'nickname',
      label: '用户名',
      width: 120,
      sortable: 'custom'
    },
    {
      prop: 'sex',
      label: '性别',
      width: 80,
      sortable: 'custom'
    },
    {
      prop: 'phone',
      label: '手机号',
      width: 130,
      sortable: 'custom'
    },
    {
      prop: 'email',
      label: '邮箱',
      width: 180,
      sortable: 'custom'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      sortable: 'custom'
    },
    {
      prop: 'status',
      label: '状态',
      width: 80,
      sortable: 'custom'
    }
  ]);

  /** 生成测试数据 */
  const generateData = (count: number) => {
    const data: any[] = [];
    for (let i = 1; i <= count; i++) {
      data.push({
        userId: i,
        username: `user${i}`,
        nickname: `用户${i}`,
        sex: i % 2 === 0 ? '女' : '男',
        phone: `1380000${String(i).padStart(4, '0')}`,
        email: `user${i}@example.com`,
        createTime: new Date(
          Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
        )
          .toISOString()
          .split('T')[0],
        status: i % 3 === 0 ? '禁用' : '正常'
      });
    }
    return data;
  };

  /** 表格数据源 */
  const datasource = generateData(1000);

  /** 列宽拖拽结束事件 */
  const handleHeaderDragend = (
    newWidth: number,
    oldWidth: number,
    column: Column,
    _event: MouseEvent
  ) => {
    console.log('列宽拖拽结束:', {
      column: column.prop || column.label,
      oldWidth,
      newWidth,
      change: newWidth - oldWidth
    });

    EleMessage.success(
      `列 "${column.label}" 宽度从 ${oldWidth}px 调整为 ${newWidth}px`
    );
  };
</script>
