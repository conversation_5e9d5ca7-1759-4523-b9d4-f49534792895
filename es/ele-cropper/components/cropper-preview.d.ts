declare const _default: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    /** 组件宽度 */
    previewWidth: {
        type: NumberConstructor;
        required: true;
    };
    /** 裁剪比例 */
    aspectRatio: NumberConstructor;
}>, {
    getPreviews: () => NodeListOf<HTMLElement> | undefined;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    /** 组件宽度 */
    previewWidth: {
        type: NumberConstructor;
        required: true;
    };
    /** 裁剪比例 */
    aspectRatio: NumberConstructor;
}>> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
export default _default;
