import { inject, computed } from "vue";
import { STR_KEY } from "../utils/common";
import { STR_KEY as STR_KEY2 } from "../utils/common";
import { CONFIG_KEY } from "./props";
import defaultLocale from "../lang/zh_CN";
function useReceiver() {
  return inject(CONFIG_KEY, {});
}
function useGlobalProps(name) {
  const globalConfig = useReceiver();
  return computed(() => globalConfig[name] ?? {});
}
function useLocale(name, props) {
  const globalConfig = useReceiver();
  const lang = computed(() => {
    const temp = globalConfig.locale ?? defaultLocale;
    if (name) {
      try {
        return Object.assign({}, temp[name] ?? {}, props == null ? void 0 : props.locale);
      } catch (e) {
        console.error(e, STR_KEY);
      }
    }
    return temp;
  });
  return { lang, globalConfig };
}
export {
  STR_KEY2 as VAL_KEY,
  useGlobalProps,
  useLocale,
  useReceiver
};
