@use '../../style/themes/default.scss' as *;
@use '../../style/util.scss' as *;

.ele-avatar-group {
  display: inline-block;
  vertical-align: top;
  position: relative;
  font-size: 0;

  .el-avatar {
    vertical-align: top;
    border: 1px solid elVar('bg-color', 'overlay');
    transition: all 0.3s ease;
    box-sizing: content-box;
    position: relative;
    cursor: pointer;
    z-index: 1;

    &:first-child {
      margin-left: 0 !important;
    }

    &:hover {
      z-index: 2;
    }
  }

  .ele-avatar-more {
    color: rgb(245, 106, 0);
    background: elVar('bg-color', 'overlay');
    display: inline-block;
  }

  .ele-avatar-more-inner {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(245, 106, 0, 0.2);
  }

  &.is-hover-open {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transition: all 0.3s ease;
      pointer-events: all;
      z-index: 9999;
    }

    &:hover {
      &::before {
        visibility: hidden;
      }

      .el-avatar {
        margin-left: 0 !important;
      }
    }
  }
}

.ele-avatar-popover.ele-popper.ele-popover {
  min-width: auto;
}

.ele-avatar-popover > .ele-popover-body {
  padding: 8px 12px;
}
